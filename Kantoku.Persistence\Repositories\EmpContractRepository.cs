using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface IEmpContractRepository
{
    Task<EmpContract?> GetById(Guid contractId);
    Task<EmpContract?> Create(EmpContract empContract);
    Task<EmpContract?> Update(EmpContract empContract);
}

public class EmpContractRepository : BaseRepository<EmpContract>, IEmpContractRepository
{
    public EmpContractRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    ) : base(context, logger, tenantContext)
    {
    }

    public async Task<EmpContract?> GetById(Guid contractId)
    {
        try
        {
            var query = context.EmpContracts
                .Where(e => e.IsDeleted == false)
                .Where(e => e.EmpContractUid == contractId);
            var contract = await query.FirstOrDefaultAsync();
            return contract;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting contract by id: {Id}", contractId);
            return null;
        }
    }

    public async Task<EmpContract?> Create(EmpContract empContract)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            empContract.IsDeleted = false;
            empContract.EmpContractUid = GuidHelper.GenerateUUIDv7();
            await context.EmpContracts.AddAsync(empContract);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(empContract.EmpContractUid);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating contract: {Contract}", empContract);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<EmpContract?> Update(EmpContract empContract)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EmpContracts.Update(empContract);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(empContract.EmpContractUid);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating contract: {Contract}", empContract);
            await transaction.RollbackAsync();
            return null;
        }
    }
}
using Kantoku.Api.Services;
using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Services;

namespace Kantoku.Api.Extensions.Services;

public static class TenantContextExtensions
{
    /// <summary>
    /// Registers HTTP-based tenant context for API scenarios
    /// This should be called after AddPersistence() to override the default StaticTenantContext
    /// </summary>
    public static IServiceCollection AddHttpTenantContext(this IServiceCollection services)
    {
        // Override the default ITenantContext registration with HTTP-based implementation
        services.AddScoped<ITenantContext, HttpTenantContext>();
        return services;
    }

    /// <summary>
    /// Registers static tenant context with specific tenant information
    /// Useful for background jobs or console applications
    /// </summary>
    public static IServiceCollection AddStaticTenantContext(
        this IServiceCollection services,
        Guid accountUid,
        Guid orgUid,
        Guid employeeUid)
    {
        services.AddScoped<ITenantContext>(provider =>
        {
            var context = provider.GetRequiredService<PostgreDbContext>();
            var logger = provider.GetRequiredService<ILogger>();

            return StaticTenantContextFactory.CreateTenantContext(
                context, logger, accountUid, orgUid, employeeUid);
        });

        return services;
    }

    /// <summary>
    /// Registers system tenant context for system-level operations
    /// Useful for background jobs that operate at system level
    /// </summary>
    public static IServiceCollection AddSystemTenantContext(this IServiceCollection services)
    {
        services.AddScoped<ITenantContext>(provider =>
        {
            var context = provider.GetRequiredService<PostgreDbContext>();
            var logger = provider.GetRequiredService<ILogger>();

            return StaticTenantContextFactory.CreateSystemContext(context, logger);
        });

        return services;
    }
}

namespace Kantoku.Persistence.Filters.Domains;

public class NotificationFilter : BaseFilter
{
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    [FromQuery(Name = "dateFrom")]
    public string? DateFrom { get; set; }

    [FromQuery(Name = "dateTo")]
    public string? DateTo { get; set; }
}

public class EmployeeNotificationFilter : NotificationFilter
{
    [FromQuery(Name = "isRead")]
    public bool? IsRead { get; set; }
}

public class OrgNotificationFilter : NotificationFilter
{
    [FromQuery(Name = "type")]
    public string? Type { get; set; }

    [FromQuery(Name = "targetType")]
    public string? TargetType { get; set; }

    [FromQuery(Name = "status")]
    public string? Status { get; set; }
}




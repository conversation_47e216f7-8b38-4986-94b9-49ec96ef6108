﻿using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Repositories;

public interface IItemPriceRepository
{
    Task<(int, IEnumerable<ItemPrice>)> GetByFilter(ItemPriceFilter filter, ItemPriceQueryableOptions options);
    Task<(int, IEnumerable<ItemPrice>)> GetByItemId(Guid itemId, ItemPriceFilter filter, ItemPriceQueryableOptions options);
    Task<ItemPrice?> GetById(Guid itemPriceId, ItemPriceQueryableOptions options);
    Task<ItemPrice?> Create(ItemPrice itemPrice, ItemPriceQueryableOptions options);
    Task<ItemPrice?> Update(ItemPrice itemPrice, ItemPriceQueryableOptions options);

    Task<Guid?> Create(ItemPrice itemPrice);
    Task<bool> Update(ItemPrice itemPrice);
}

public class ItemPriceRepository : BaseRepository<ItemPriceRepository>, IItemPriceRepository
{
    private readonly IItemPriceQueryable queryable;
    public ItemPriceRepository(
        PostgreDbContext context,
        IItemPriceQueryable queryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.queryable = queryable;
    }

    public async Task<(int, IEnumerable<ItemPrice>)> GetByFilter(ItemPriceFilter filter, ItemPriceQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = queryable.GetItemPricesQueryFiltered(filter, options);

            var total = await query
                .CountAsync();

            var result = await query
                .OrderByDescending(i => i.ValidTo)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();
            return (total, result);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting item prices");
            return (0, []);
        }
    }

    public async Task<(int, IEnumerable<ItemPrice>)> GetByItemId(Guid itemId, ItemPriceFilter filter, ItemPriceQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = queryable.GetItemPricesQueryFiltered(filter, options)
                .Where(p => p.ItemUid == itemId);

            var total = await query
                .CountAsync();

            var result = await query
                .OrderByDescending(i => i.ValidFrom)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (total, result);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting item prices");
            return (0, []);
        }
    }

    public async Task<ItemPrice?> GetById(Guid itemPriceId, ItemPriceQueryableOptions options)
    {
        try
        {
            var query = queryable.GetItemPricesQueryIncluded(options)
                    .Where(p => p.ItemPriceUid == itemPriceId);

            var result = await query
                .FirstOrDefaultAsync();

            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting ItemPrice by id");
            return null;
        }
    }

    public async Task<IEnumerable<ItemPrice>> GetByItemId(Guid itemId, ItemPriceQueryableOptions options)
    {
        try
        {
            var query = queryable.GetItemPricesQueryIncluded(options)
                .Where(p => p.ItemUid == itemId);

            var result = await query
                .ToListAsync();

            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting ItemPrice by item id");
            return [];
        }
    }

    public async Task<ItemPrice?> Create(ItemPrice itemPrice, ItemPriceQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.ItemPrices.AddAsync(itemPrice);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(itemPrice.ItemPriceUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating ItemPrice");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(ItemPrice itemPrice)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.ItemPrices.AddAsync(itemPrice);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return itemPrice.ItemPriceUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating ItemPrice");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<ItemPrice?> Update(ItemPrice itemPrice, ItemPriceQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.ItemPrices.Update(itemPrice);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(itemPrice.ItemPriceUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating ItemPrice");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(ItemPrice itemPrice)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.ItemPrices.Update(itemPrice);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating ItemPrice");
            await transaction.RollbackAsync();
            return false;
        }
    }
}

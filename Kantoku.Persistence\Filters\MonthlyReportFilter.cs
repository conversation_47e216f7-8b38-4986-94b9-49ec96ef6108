using System.ComponentModel.DataAnnotations;
namespace Kantoku.Persistence.Filters.Domains;

public class MonthlyReportFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search for name or code of employee
    /// </summary>
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    /// <summary>
    /// Working status of employee  
    /// </summary>
    [FromQuery(Name = "workingStatus")]
    public string? WorkingStatus { get; set; }

    /// <summary>
    /// Report from date
    /// </summary>
    [FromQuery(Name = "dateFrom")]
    [Required]
    public required string ReportFrom { get; set; }

    /// <summary>
    /// Report to date
    /// </summary>
    [FromQuery(Name = "dateTo")]
    [Required]
    public required string ReportTo { get; set; }
}
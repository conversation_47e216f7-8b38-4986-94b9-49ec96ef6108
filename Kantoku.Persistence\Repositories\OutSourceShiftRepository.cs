using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface IOutSourceShiftRepository
{
    Task<(IEnumerable<OutSourceShift>, int)> GetByFilter(OutSourceShiftFilter filter, OutSourceShiftQueryableOptions options);
    Task<OutSourceShift?> GetById(Guid outSourceShiftId, OutSourceShiftQueryableOptions options);
    Task<IEnumerable<OutSourceShift>> GetByIds(IEnumerable<Guid> outSourceShiftIds, OutSourceShiftQueryableOptions options);
    Task<IEnumerable<OutSourceShift>> GetByProjectScheduleId(Guid projectScheduleId, OutSourceShiftQueryableOptions options);
    Task<OutSourceShift?> Create(OutSourceShift outSourceShift, OutSourceShiftQueryableOptions options);
    Task<OutSourceShift?> Update(OutSourceShift outSourceShift, OutSourceShiftQueryableOptions options);
    Task<IEnumerable<OutSourceShift>> CreateMultiple(IEnumerable<OutSourceShift> outSourceShifts, OutSourceShiftQueryableOptions options);
    Task<IEnumerable<OutSourceShift>> UpdateMultiple(IEnumerable<OutSourceShift> outSourceShifts, OutSourceShiftQueryableOptions options);
}

public class OutSourceShiftRepository : BaseRepository<OutSourceShift>, IOutSourceShiftRepository
{
    private readonly IOutSourceShiftQueryable outSourceShiftQueryable;
    public OutSourceShiftRepository(
        PostgreDbContext context,
        IOutSourceShiftQueryable outSourceShiftQueryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.outSourceShiftQueryable = outSourceShiftQueryable;
    }

    public async Task<(IEnumerable<OutSourceShift>, int)> GetByFilter(OutSourceShiftFilter filter, OutSourceShiftQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = outSourceShiftQueryable.GetOutSourceShiftQueryFiltered(filter, options);

            var outSourceShifts = await query
                .OrderBy(p => p.OutSourceShiftUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query
                .CountAsync();
            return (outSourceShifts, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all outSourceShifts");
            return ([], 0);
        }
    }

    public async Task<OutSourceShift?> GetById(Guid outSourceShiftId, OutSourceShiftQueryableOptions options)
    {
        try
        {
            var query = outSourceShiftQueryable.GetOutSourceShiftQueryIncluded(options)
                .Where(p => p.OutSourceShiftUid == outSourceShiftId);

            var outSourceShift = await query
                .FirstOrDefaultAsync();

            return outSourceShift;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting outSourceShift by id {OutSourceShiftId}", outSourceShiftId);
            return null;
        }
    }

    public async Task<IEnumerable<OutSourceShift>> GetByIds(IEnumerable<Guid> outSourceShiftIds, OutSourceShiftQueryableOptions options)
    {
        try
        {
            var query = outSourceShiftQueryable.GetOutSourceShiftQueryIncluded(options)
                .Where(p => outSourceShiftIds.Contains(p.OutSourceShiftUid));

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting outSourceShifts by ids {OutSourceShiftIds}", outSourceShiftIds);
            return [];
        }
    }

    public async Task<IEnumerable<OutSourceShift>> GetByProjectScheduleId(Guid projectScheduleId, OutSourceShiftQueryableOptions options)
    {
        try
        {
            var query = outSourceShiftQueryable.GetOutSourceShiftQueryIncluded(options)
                .Where(p => p.ProjectScheduleUid == projectScheduleId);

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting outSourceShifts by project schedule id {ProjectScheduleId}", projectScheduleId);
            return [];
        }
    }

    public async Task<OutSourceShift?> Create(OutSourceShift outSourceShift, OutSourceShiftQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.OutSourceShifts.AddAsync(outSourceShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(outSourceShift.OutSourceShiftUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating outSourceShift {OutSourceShift}", outSourceShift);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<IEnumerable<OutSourceShift>> CreateMultiple(IEnumerable<OutSourceShift> outSourceShifts, OutSourceShiftQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.OutSourceShifts.AddRangeAsync(outSourceShifts);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetByIds(outSourceShifts.Select(o => o.OutSourceShiftUid), options);
            }
            await transaction.RollbackAsync();
            return [];
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating outSourceShifts {OutSourceShifts}", outSourceShifts);
            await transaction.RollbackAsync();
            return [];
        }
    }

    public async Task<OutSourceShift?> Update(OutSourceShift outSourceShift, OutSourceShiftQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.OutSourceShifts.Update(outSourceShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(outSourceShift.OutSourceShiftUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating outSourceShift {OutSourceShift}", outSourceShift);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<IEnumerable<OutSourceShift>> UpdateMultiple(IEnumerable<OutSourceShift> outSourceShifts, OutSourceShiftQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.OutSourceShifts.UpdateRange(outSourceShifts);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetByIds(outSourceShifts.Select(o => o.OutSourceShiftUid), options);
            }
            await transaction.RollbackAsync();
            return [];
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating outSourceShifts {OutSourceShifts}", outSourceShifts);
            await transaction.RollbackAsync();
            return [];
        }
    }
}


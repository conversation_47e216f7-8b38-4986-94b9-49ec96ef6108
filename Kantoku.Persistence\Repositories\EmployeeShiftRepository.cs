using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface IEmployeeShiftRepository
{
    Task<IEnumerable<EmployeeShift>> GetByFilter(EmployeeShiftFilter filter, EmployeeShiftQueryableOptions options);
    Task<IEnumerable<EmployeeShift>> GetByIds(IEnumerable<Guid> employeeShiftIds, EmployeeShiftQueryableOptions options);
    Task<IEnumerable<EmployeeShift>> GetByProjectScheduleId(Guid projectScheduleId, EmployeeShiftQueryableOptions options);
    Task<EmployeeShift?> GetById(Guid employeeShiftId, EmployeeShiftQueryableOptions options);
    Task<bool> HasUnfinishedShift(Guid employeeId);
    Task<EmployeeShift?> Create(EmployeeShift employeeShift, EmployeeShiftQueryableOptions options);
    Task<IEnumerable<EmployeeShift>> CreateMultiple(IEnumerable<EmployeeShift> employeeShifts, EmployeeShiftQueryableOptions options);
    Task<EmployeeShift?> Update(EmployeeShift employeeShift, EmployeeShiftQueryableOptions options);
    Task<bool> UpdateMultiple(IEnumerable<EmployeeShift> employeeShifts);

    Task<Guid?> Create(EmployeeShift employeeShift);
    Task<bool> Update(EmployeeShift employeeShift);
}

public class EmployeeShiftRepository : BaseRepository<EmployeeShift>, IEmployeeShiftRepository
{
    private readonly IEmployeeShiftQueryable shiftQueryable;
    public EmployeeShiftRepository(PostgreDbContext context, IEmployeeShiftQueryable shiftQueryable, ITenantContext tenantContext, Serilog.ILogger logger)
        : base(context, logger, tenantContext)
    {
        this.shiftQueryable = shiftQueryable;
    }

    public async Task<EmployeeShift?> GetById(Guid employeeShiftId, EmployeeShiftQueryableOptions options)
    {
        try
        {
            var query = shiftQueryable.GetShiftsQueryIncluded(options)
            .Where(s => s.EmployeeShiftUid == employeeShiftId);

            var employeeShift = await query.FirstOrDefaultAsync();
            return employeeShift;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee shift by id: {EmployeeShiftId}", employeeShiftId);
            return null;
        }
    }

    public async Task<IEnumerable<EmployeeShift>> GetByFilter(EmployeeShiftFilter filter, EmployeeShiftQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = shiftQueryable.GetShiftsQueryFiltered(filter, options);

            var shifts = await query
                .OrderBy(es => es.CreatedTime)
                .ToListAsync();

            return shifts;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee shifts by filter");
            return [];
        }
    }

    public async Task<IEnumerable<EmployeeShift>> GetByIds(IEnumerable<Guid> employeeShiftIds, EmployeeShiftQueryableOptions options)
    {
        try
        {
            var query = shiftQueryable.GetShiftsQueryIncluded(options)
                .Where(s => employeeShiftIds.Contains(s.EmployeeShiftUid));

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee shifts by ids: {EmployeeShiftIds}", employeeShiftIds);
            return [];
        }
    }

    public async Task<IEnumerable<EmployeeShift>> GetByProjectScheduleId(Guid projectScheduleId, EmployeeShiftQueryableOptions options)
    {
        try
        {
            var query = shiftQueryable.GetShiftsQueryIncluded(options)
                .Where(s => s.ProjectScheduleUid == projectScheduleId);

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee shifts by project schedule id: {ProjectScheduleId}", projectScheduleId);
            return [];
        }
    }

    public async Task<bool> HasUnfinishedShift(Guid employeeId)
    {
        try
        {
            var query = shiftQueryable.GetShiftsQueryIncluded(new EmployeeShiftQueryableOptions { IncludedProject = true })
                .Where(s => s.EmployeeUid == employeeId)
                .Where(s => s.CheckInTime != null && s.CheckOutTime == null && s.AutoCheckOutTime == null)
                .Where(s => s.CheckInTime!.Value.Date == DateTime.Now.Date);

            var result = await query.ToListAsync();
            return result.Count > 0;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error checking if employee has unfinished shift: {EmployeeId}", employeeId);
            return false;
        }
    }

    public async Task<EmployeeShift?> Create(EmployeeShift employeeShift, EmployeeShiftQueryableOptions? options = null)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.EmployeeShifts.AddAsync(employeeShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(employeeShift.EmployeeShiftUid, options ?? new EmployeeShiftQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating employee shift: {EmployeeShift}", employeeShift);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(EmployeeShift employeeShift)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.EmployeeShifts.AddAsync(employeeShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return employeeShift.EmployeeShiftUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating employee shift: {EmployeeShift}", employeeShift);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<IEnumerable<EmployeeShift>> CreateMultiple(IEnumerable<EmployeeShift> employeeShifts, EmployeeShiftQueryableOptions? options = null)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.EmployeeShifts.AddRangeAsync(employeeShifts);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetByIds(employeeShifts.Select(e => e.EmployeeShiftUid), options ?? new EmployeeShiftQueryableOptions());
            }
            await transaction.RollbackAsync();
            return [];
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating multiple employee shifts: {EmployeeShifts}", employeeShifts);
            await transaction.RollbackAsync();
            return [];
        }
    }

    public async Task<EmployeeShift?> Update(EmployeeShift employeeShift, EmployeeShiftQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EmployeeShifts.Update(employeeShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(employeeShift.EmployeeShiftUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating employee shift: {EmployeeShift}", employeeShift);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(EmployeeShift employeeShift)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EmployeeShifts.Update(employeeShift);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating employee shift: {EmployeeShift}", employeeShift);
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<bool> UpdateMultiple(IEnumerable<EmployeeShift> employeeShifts)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EmployeeShifts.UpdateRange(employeeShifts);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating multiple employee shifts: {EmployeeShifts}", employeeShifts);
            await transaction.RollbackAsync();
            return false;
        }
    }
}

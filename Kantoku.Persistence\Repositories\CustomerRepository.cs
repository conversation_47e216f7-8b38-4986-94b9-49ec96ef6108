﻿using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Repositories;

public interface ICustomerRepository
{
    Task<(IEnumerable<Customer>, int)> GetByFilter(CustomerFilter filter, CustomerQueryableOptions options);
    Task<Customer?> GetById(Guid customerId, CustomerQueryableOptions options);
    Task<Customer?> Create(Customer customer);
    Task<Customer?> Update(Customer customer);
}

public class CustomerRepository : BaseRepository<Customer>, ICustomerRepository
{
    private readonly ICustomerQueryable customerQueryable;

    public CustomerRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        ICustomerQueryable customerQueryable
    ) : base(context, logger, tenantContext)
    {
        this.customerQueryable = customerQueryable;
    }

    public async Task<(IEnumerable<Customer>, int)> GetByFilter(CustomerFilter filter, CustomerQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = customerQueryable.GetCustomerQueryFilter(filter, options);

            var total = await query
                .CountAsync();

            var result = await query
                .OrderBy(c => c.CustomerUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all Customers");
            return ([], 0);
        }
    }

    public async Task<Customer?> GetById(Guid customerId, CustomerQueryableOptions options)
    {
        try
        {
            var query = customerQueryable.GetCustomerQueryIncluded(options)
                .Where(c => c.CustomerUid == customerId);

            var result = await query
                .FirstOrDefaultAsync();

            return result;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting Customer by id: {customerId}", customerId);
            return null;
        }
    }

    public async Task<Customer?> Create(Customer customer)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Customers.AddAsync(customer);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(customer.CustomerUid, new CustomerQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating Customer");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Customer?> Update(Customer customer)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Customers.Update(customer);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(customer.CustomerUid, new CustomerQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating Customer");
            await transaction.RollbackAsync();
            return null;
        }
    }
}

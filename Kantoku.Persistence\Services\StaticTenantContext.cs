using Kantoku.Persistence.Contexts;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Services;

/// <summary>
/// Static implementation of tenant context for background jobs, console apps, or testing
/// </summary>
public class StaticTenantContext : ITenantContext
{
    private readonly PostgreDbContext _context;
    private readonly Serilog.ILogger _logger;
    private readonly TenantInfo _tenantInfo;

    public StaticTenantContext(
        PostgreDbContext context,
        Serilog.ILogger logger,
        TenantInfo tenantInfo)
    {
        _context = context;
        _logger = logger.ForContext<StaticTenantContext>();
        _tenantInfo = tenantInfo;
    }

    public Guid GetCurrentAccountUid() => _tenantInfo.AccountUid;

    public Guid GetCurrentOrgUid() => _tenantInfo.OrgUid;

    public Guid GetCurrentEmployeeUid() => _tenantInfo.EmployeeUid;

    public bool TryGetCurrentAccountUid(out Guid accountUid)
    {
        accountUid = _tenantInfo.AccountUid;
        return accountUid != Guid.Empty;
    }

    public bool TryGetCurrentOrgUid(out Guid orgUid)
    {
        orgUid = _tenantInfo.OrgUid;
        return orgUid != Guid.Empty;
    }

    public bool TryGetCurrentEmployeeUid(out Guid employeeUid)
    {
        employeeUid = _tenantInfo.EmployeeUid;
        return employeeUid != Guid.Empty;
    }

    public async Task<bool> IsSuperUserAsync()
    {
        if (_tenantInfo.AccountUid == Guid.Empty)
        {
            return false;
        }

        var account = await _context.Accounts
            .Where(a => a.AccountUid == _tenantInfo.AccountUid)
            .FirstOrDefaultAsync();

        if (account is null)
        {
            return false;
        }

        return account.AccountType.Equals("SUPER_USER"); // Use constant from your codebase
    }

    public async Task<bool> IsOrgSuperUserAsync()
    {
        if (_tenantInfo.AccountUid == Guid.Empty || _tenantInfo.OrgUid == Guid.Empty)
        {
            return false;
        }

        var employee = await _context.Employees
            .Include(e => e.EmployeeRoles)
            .ThenInclude(er => er.Role)
            .Where(e => e.OrgUid == _tenantInfo.OrgUid && e.AccountUid == _tenantInfo.AccountUid)
            .FirstOrDefaultAsync();

        if (employee is null)
        {
            return false;
        }

        return employee.IsOrgAdmin || employee.IsOrgOwner || employee.IsHidden ||
               employee.EmployeeRoles.Any(er => er.Role.IsHidden);
    }
}

/// <summary>
/// Factory for creating static tenant contexts
/// </summary>
public static class StaticTenantContextFactory
{
    /// <summary>
    /// Creates a tenant context for system operations (no specific tenant)
    /// </summary>
    public static StaticTenantContext CreateSystemContext(PostgreDbContext context, Serilog.ILogger logger)
    {
        return new StaticTenantContext(context, logger, new TenantInfo
        {
            AccountUid = Guid.Empty,
            OrgUid = Guid.Empty,
            EmployeeUid = Guid.Empty,
            IsSuperUser = true,
            IsOrgSuperUser = true
        });
    }

    /// <summary>
    /// Creates a tenant context for a specific tenant
    /// </summary>
    public static StaticTenantContext CreateTenantContext(
        PostgreDbContext context,
        Serilog.ILogger logger,
        Guid accountUid,
        Guid orgUid,
        Guid employeeUid)
    {
        return new StaticTenantContext(context, logger, new TenantInfo
        {
            AccountUid = accountUid,
            OrgUid = orgUid,
            EmployeeUid = employeeUid,
            IsSuperUser = false,
            IsOrgSuperUser = false
        });
    }
}

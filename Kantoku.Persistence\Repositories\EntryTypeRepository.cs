﻿using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Repositories;

public interface IEntryTypeRepository
{
    Task<(IEnumerable<EntryType>, int)> GetByFilter(EntryTypeFilter filter, EntryTypeQueryableOptions options);
    Task<EntryType?> GetById(Guid entryTypeUid, EntryTypeQueryableOptions options);
    Task<EntryType?> Create(EntryType entryType, EntryTypeQueryableOptions options);
    Task<EntryType?> Update(EntryType entryType, EntryTypeQueryableOptions options);

    Task<Guid?> Create(EntryType entryType);
    Task<bool> Update(EntryType entryType);
}

public class EntryTypeRepository : BaseRepository<EntryTypeRepository>, IEntryTypeRepository
{
    private readonly IEntryTypeQueryable entryTypeQueryable;
    public EntryTypeRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        IEntryTypeQueryable entryTypeQueryable
    ) : base(context, logger, tenantContext)
    {
        this.entryTypeQueryable = entryTypeQueryable;
    }

    public async Task<(IEnumerable<EntryType>, int)> GetByFilter(EntryTypeFilter filter, EntryTypeQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = entryTypeQueryable.GetEntryTypeQueryFiltered(filter, options);

            var total = await query
                .CountAsync();

            var result = await query
                .OrderBy(et => et.EntryTypeUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting entry type list paginated");
            return ([], 0);
        }
    }

    public async Task<EntryType?> GetById(Guid entryTypeUid, EntryTypeQueryableOptions options)
    {
        try
        {
            var query = entryTypeQueryable.GetEntryTypeQuery(options)
                .Where(e => e.EntryTypeUid == entryTypeUid);

            var result = await query
                .FirstOrDefaultAsync();

            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting entry type by id");
            return null;
        }
    }

    public async Task<EntryType?> Create(EntryType entryType, EntryTypeQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.EntryTypes.AddAsync(entryType);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return entryType;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating entry type");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<EntryType?> Update(EntryType entryType, EntryTypeQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EntryTypes.Update(entryType);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return entryType;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating entry type");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(EntryType entryType)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.EntryTypes.AddAsync(entryType);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return entryType.EntryTypeUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating entry type");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(EntryType entryType)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EntryTypes.Update(entryType);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating entry type");
            await transaction.RollbackAsync();
            return false;
        }
    }
}

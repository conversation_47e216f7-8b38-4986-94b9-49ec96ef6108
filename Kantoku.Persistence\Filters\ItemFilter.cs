namespace Kantoku.Persistence.Filters.Domains;

public class ItemFilter : BaseFilter
{
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    [FromQuery(Name = "categoryId")]
    public Guid? CategoryId { get; set; }

    [FromQuery(Name = "manufacturerId")]
    public Guid? ManufacturerId { get; set; }

    [FromQuery(Name = "manufacturerIds")]
    public IEnumerable<Guid>? ManufacturerIds { get; set; }

    [FromQuery(Name = "size")]
    public string? Size { get; set; }

    [FromQuery(Name = "serialNumber")]
    public string? SerialNumber { get; set; }
}


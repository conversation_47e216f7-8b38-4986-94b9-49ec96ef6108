namespace Kantoku.Persistence.Filters.Domains;

public class OutSourceShiftFilter : BaseFilter
{
    [FromQuery(Name = "outSourceId")]
    public Guid? OutSourceId { get; set; }

    [FromQuery(Name = "projectId")]
    public Guid? ProjectId { get; set; }

    [FromQuery(Name = "projectScheduleId")]
    public Guid? ProjectScheduleId { get; set; }

    [FromQuery(Name = "workingDate")]
    public string? WorkingDate { get; set; }

    [FromQuery(Name = "workingDateFrom")]
    public string? WorkingDateFrom { get; set; }

    [FromQuery(Name = "workingDateTo")]
    public string? WorkingDateTo { get; set; }

    [FromQuery(Name = "role")]
    public string? Role { get; set; }
}
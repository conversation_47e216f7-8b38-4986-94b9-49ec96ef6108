using Kantoku.Application.Interfaces;
using Kantoku.Persistence.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Kantoku.Persistence
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddDbContext(
            this IServiceCollection services,
            IWebHostEnvironment env
        )
        {
            services.AddDbContext<PostgreDbContext>(options =>
            {
                if (env.IsDevelopment())
                {
                    options.EnableDetailedErrors();
                    options.EnableSensitiveDataLogging();
                }
            });
            return services;
        }

        public static IServiceCollection AddRepositories(
            this IServiceCollection services
        )
        {
            services.AddScoped<IAccountRepository, AccountRepository>();
            return services;
        }
    }
}
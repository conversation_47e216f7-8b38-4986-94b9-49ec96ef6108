using Kantoku.Api.Filters.Domains;
using Microsoft.AspNetCore.Mvc;
namespace Kantoku.Api.Filters.Domains;

public class ItemPriceFilter : BaseFilter
{
    [FromQuery(Name = "vendorKeyword")]
    public string? VendorKeyword { get; set; }

    [FromQuery(Name = "fromDate")]
    public string? FromDate { get; set; }

    [FromQuery(Name = "toDate")]
    public string? ToDate { get; set; }

    [FromQuery(Name = "priceMin")]
    public int? PriceMin { get; set; }

    [FromQuery(Name = "priceMax")]
    public int? PriceMax { get; set; }
}


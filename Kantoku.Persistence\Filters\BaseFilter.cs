namespace Kantoku.Persistence.Filters;

/// <summary>
/// Base filter for persistence layer - contains only domain-specific filtering logic
/// No HTTP-specific attributes or concerns
/// </summary>
public abstract class BaseFilter
{
    /// <summary>
    /// Organization ID for tenant filtering (set by tenant context)
    /// </summary>
    public Guid? OrgId { get; set; }

    /// <summary>
    /// The page number (default: 1)
    /// </summary>
    public int PageNum { get; set; } = 1;

    /// <summary>
    /// The page size (default: 10)
    /// </summary>
    public int PageSize { get; set; } = 10;

    /// <summary>
    /// Validates and normalizes the filter values
    /// </summary>
    public virtual void Validate()
    {
        if (PageNum <= 0) PageNum = 1;
        if (PageSize <= 0) PageSize = 10;
        if (PageSize > 100) PageSize = 100; // Prevent excessive page sizes
    }
}

/// <summary>
/// Interface for mapping API filters to domain filters
/// </summary>
public interface IFilterMapper<TApiFilter, TDomainFilter>
    where TApiFilter : class
    where TDomainFilter : BaseFilter
{
    TDomainFilter MapToDomain(TApiFilter apiFilter);
}

/// <summary>
/// Base implementation for filter mapping
/// </summary>
public abstract class FilterMapperBase<TApiFilter, TDomainFilter> : IFilterMapper<TApiFilter, TDomainFilter>
    where TApiFilter : class
    where TDomainFilter : BaseFilter, new()
{
    public virtual TDomainFilter MapToDomain(TApiFilter apiFilter)
    {
        var domainFilter = new TDomainFilter();
        MapSpecificProperties(apiFilter, domainFilter);
        domainFilter.Validate();
        return domainFilter;
    }

    protected abstract void MapSpecificProperties(TApiFilter apiFilter, TDomainFilter domainFilter);
}

﻿using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Seeders.Categories;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface ICategoryRepository
{
    Task<(IEnumerable<Category>, int)> GetByFilter(CategoryFilter filter, CategoryQueryableOptions options);
    Task<IEnumerable<Category>> GetRoot(CategoryQueryableOptions options);
    Task<Category?> GetById(Guid categoryId, CategoryQueryableOptions options);
    Task<Category?> GetByCode(string code, CategoryQueryableOptions options);
    Task<Category?> Create(Category category, CategoryQueryableOptions options);
    Task<Category?> Update(Category category, CategoryQueryableOptions options);

    Task<Guid?> Create(Category category);
    Task<bool> Update(Category category);
}

public class CategoryRepository : BaseRepository<Category>, ICategoryRepository
{
    private readonly ICategoryQueryable categoryQueryable;
    private readonly ICategorySeeder categorySeeder;

    public CategoryRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        ICategoryQueryable categoryQueryable,
        ICategorySeeder categorySeeder
    ) : base(context, logger, tenantContext)
    {
        this.categoryQueryable = categoryQueryable;
        this.categorySeeder = categorySeeder;
    }

    public async Task<(IEnumerable<Category>, int)> GetByFilter(CategoryFilter filter, CategoryQueryableOptions options)
    {
        try
        {
            var hasInitialData = await context.Categories.Where(c => c.IsDefault).AnyAsync();
            if (!hasInitialData)
            {
                await categorySeeder.Seed();
            }

            filter = ValidateFilter(filter);
            var query = categoryQueryable.GetCategoriesQueryFiltered(filter, options);

            var total = await query
                .CountAsync();

            var result = await query
                .OrderBy(c => c.CategoryUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all categories");
            return ([], 0);
        }
    }

    public async Task<IEnumerable<Category>> GetRoot(CategoryQueryableOptions options)
    {
        try
        {
            var filter = new CategoryFilter
            {
                OrgId = GetCurrentOrgUid(),
            };
            var query = categoryQueryable.GetCategoriesQueryFiltered(filter, options)
                .Where(c => c.ParentUid == null);

            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting root categories");
            return [];
        }
    }

    public async Task<Category?> GetById(Guid categoryId, CategoryQueryableOptions options)
    {
        try
        {
            var query = categoryQueryable.GetCategoriesQueryIncluded(options)
                .Where(c => c.CategoryUid == categoryId);

            var result = await query
                .FirstOrDefaultAsync();
            return result;
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Error getting category by id: {categoryId}", categoryId);
            return null;
        }
    }

    public async Task<Category?> GetByCode(string code, CategoryQueryableOptions options)
    {
        try
        {
            var query = categoryQueryable.GetCategoriesQueryIncluded(options)
                .Where(c => c.CategoryCode == code);
            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting category by code: {code}", code);
            return null;
        }
    }

    public async Task<Category?> Create(Category category, CategoryQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Categories.AddAsync(category);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(category.CategoryUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating category");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Category?> Update(Category category, CategoryQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Categories.Update(category);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(category.CategoryUid, options);
            }
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating category");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Guid?> Create(Category category)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Categories.AddAsync(category);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return category.CategoryUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating category");
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> Update(Category category)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Categories.Update(category);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating category");
            await transaction.RollbackAsync();
            return false;
        }
    }
}

using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Repositories;

public interface IEmployeeLeaveRepository : IBaseRepository<EmployeeLeave>
{
    Task<(IEnumerable<EmployeeLeave>, int)> GetByFilter(EmployeeLeaveFilter filter, EmployeeLeaveQueryableOptions options);
    Task<IEnumerable<EmployeeLeave>> GetByEmployeeIdAndDateRange(Guid employeeId, DateOnly dateFrom, DateOnly dateTo, EmployeeLeaveQueryableOptions options);
    Task<EmployeeLeave?> GetById(Guid employeeLeaveId, EmployeeLeaveQueryableOptions options);
    Task<EmployeeLeave?> GetCurrentByEmployeeId(Guid employeeId, EmployeeLeaveQueryableOptions options);
    Task<EmployeeLeave?> Create(EmployeeLeave employeeLeave);
    Task<EmployeeLeave?> Update(EmployeeLeave employeeLeave);
}

public class EmployeeLeaveRepository : BaseRepository<EmployeeLeave>, IEmployeeLeaveRepository
{
    private readonly IEmployeeLeaveQueryable employeeLeaveQueryable;
    private readonly IEmployeeQueryable employeeQueryable;
    public EmployeeLeaveRepository(
        PostgreDbContext context,
        IEmployeeLeaveQueryable employeeLeaveQueryable,
        IEmployeeQueryable employeeQueryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
    : base(context, logger, tenantContext)
    {
        this.employeeLeaveQueryable = employeeLeaveQueryable;
        this.employeeQueryable = employeeQueryable;
    }

    public async Task<(IEnumerable<EmployeeLeave>, int)> GetByFilter(EmployeeLeaveFilter filter, EmployeeLeaveQueryableOptions options)
    {
        try
        {
            var employeeFilter = new EmployeeFilter();
            employeeFilter = ValidateFilter(employeeFilter);
            var currentYear = DateOnly.TryParse(filter.QueryFrom, out var currentYearDate) ? currentYearDate.Year : DateTime.Now.Year;
            var employeeQuery = employeeQueryable.GetEmployeesQueryFiltered(employeeFilter, new EmployeeQueryableOptions())
                .Where(e => !e.IsHidden)
                .Where(e => e.EmployeeLeaves.Where(el =>
                    el.EmployeeUid == e.EmployeeUid &&
                    el.BaseLeaveExpire >= new DateOnly(currentYear, 1, 1) &&
                    el.BaseLeaveExpire <= new DateOnly(currentYear, 12, 31)
                ).Count() < 1);
            var unsettleReportEmployees = await employeeQuery.Select(e => e.EmployeeUid).ToListAsync();
            if (unsettleReportEmployees.Count > 0)
            {
                var newLeave = unsettleReportEmployees.Select(uid => new EmployeeLeave
                {
                    EmployeeLeaveUid = GuidHelper.GenerateUUIDv7(),
                    EmployeeUid = uid,
                    BaseLeaveExpire = new DateOnly(currentYear, 12, 31),
                    BaseLeave = 0,
                    SelfTakenLeave = 0,
                    OrgTakenLeave = 0,
                });
                await context.EmployeeLeaves.AddRangeAsync(newLeave);
                var records = await context.SaveChangesAsync();
            }

            filter = ValidateFilter(filter);
            var leavesQuery = employeeLeaveQueryable.GetEmployeeLeaveQueryFiltered(filter, options);

            var leaves = await leavesQuery
                .OrderByDescending(e => e.CreatedTime)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await leavesQuery
                .CountAsync();
            return (leaves, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee leaves");
            return (new List<EmployeeLeave>(), 0);
        }
    }

    public async Task<EmployeeLeave?> GetById(Guid employeeLeaveId, EmployeeLeaveQueryableOptions options)
    {
        try
        {
            var query = employeeLeaveQueryable.GetEmployeeLeaveQuery(options)
                .Where(el => el.EmployeeLeaveUid == employeeLeaveId);

            var employeeLeave = await query.FirstOrDefaultAsync();
            return employeeLeave;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee leave by id: {EmployeeLeaveId}", employeeLeaveId);
            return null;
        }
    }

    public async Task<IEnumerable<EmployeeLeave>> GetByEmployeeIdAndDateRange(Guid employeeId, DateOnly dateFrom, DateOnly dateTo, EmployeeLeaveQueryableOptions options)
    {
        try
        {
            var query = employeeLeaveQueryable.GetEmployeeLeaveQueryIncluded(options)
                        .Where(e => e.EmployeeUid == employeeId)
                        .Where(e => e.BaseLeaveExpire >= dateFrom && e.BaseLeaveExpire <= dateTo);

            var employeeLeaves = await query
                .OrderBy(e => e.BaseLeaveExpire)
                .ToListAsync();
            return employeeLeaves;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employee leaves by id: {EmployeeId}", employeeId);
            return [];
        }
    }

    public async Task<EmployeeLeave?> GetCurrentByEmployeeId(Guid employeeId, EmployeeLeaveQueryableOptions options)
    {
        try
        {
            var query = employeeLeaveQueryable.GetEmployeeLeaveQueryFiltered(new EmployeeLeaveFilter(), options)
                .Where(e => e.EmployeeUid == employeeId);

            var employeeLeave = await query
                .OrderByDescending(e => e.BaseLeaveExpire)
                .FirstOrDefaultAsync();
            return employeeLeave;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting current employee leave by id: {EmployeeId}", employeeId);
            return null;
        }
    }

    public async Task<EmployeeLeave?> Create(EmployeeLeave employeeLeave)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            var res = await context.EmployeeLeaves.AddAsync(employeeLeave);
            var affectedRows = await context.SaveChangesAsync();
            if (affectedRows > 0)
            {
                await transaction.CommitAsync();
                return await GetById(employeeLeave.EmployeeLeaveUid, new EmployeeLeaveQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating or updating employee leave: {EmployeeLeave}", employeeLeave);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<EmployeeLeave?> Update(EmployeeLeave employeeLeave)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EmployeeLeaves.Update(employeeLeave);
            var affectedRows = await context.SaveChangesAsync();
            if (affectedRows > 0)
            {
                await transaction.CommitAsync();
                return await GetById(employeeLeave.EmployeeLeaveUid, new EmployeeLeaveQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating employee leave: {EmployeeLeave}", employeeLeave);
            await transaction.RollbackAsync();
            return null;
        }
    }
}
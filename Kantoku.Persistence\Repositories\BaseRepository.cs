using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Services;
using Kantoku.Api.Filters.Domains;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Repositories;

public interface IBaseRepository<T>
{
    Guid GetCurrentAccountUid();
    Guid GetCurrentEmployeeUid();
    Guid GetCurrentOrgUid();
    dynamic ValidateFilter(BaseFilter filter);
    Task<bool> IsSuperUser();
    Task<bool> IsOrgSuperUser();
}

public abstract class BaseRepository<T> : IBaseRepository<T>
{
    protected readonly PostgreDbContext context;
    protected readonly Serilog.ILogger logger;
    protected readonly ITenantContext tenantContext;

    public BaseRepository(PostgreDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext)
    {
        this.context = context;
        this.logger = logger.ForContext<T>();
        this.tenantContext = tenantContext;
    }

    public Guid GetCurrentAccountUid()
    {
        return tenantContext.GetCurrentAccountUid();
    }

    public Guid GetCurrentEmployeeUid()
    {
        return tenantContext.GetCurrentEmployeeUid();
    }

    public Guid GetCurrentOrgUid()
    {
        return tenantContext.GetCurrentOrgUid();
    }

    public dynamic ValidateFilter(BaseFilter filter)
    {
        filter.OrgId = GetCurrentOrgUid();
        return filter;
    }

    public async Task<bool> IsSuperUser()
    {
        return await tenantContext.IsSuperUserAsync();
    }

    public async Task<bool> IsOrgSuperUser()
    {
        return await tenantContext.IsOrgSuperUserAsync();
    }
}
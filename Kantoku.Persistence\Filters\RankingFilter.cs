namespace Kantoku.Persistence.Filters.Domains;

public class RankingFilter : BaseFilter
{
    /// <summary>
    /// The keyword to search for work shifts (name or code)
    /// </summary>
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    /// <summary>
    /// The minimum value of the ranking
    /// </summary>
    [FromQuery(Name = "minValue")]
    public float? MinValue { get; set; }

    /// <summary>
    /// The maximum value of the ranking
    /// </summary>
    [FromQuery(Name = "maxValue")]
    public float? MaxValue { get; set; }
}


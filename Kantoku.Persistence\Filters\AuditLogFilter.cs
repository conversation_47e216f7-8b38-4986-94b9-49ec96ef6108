namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for audit log queries - contains only business logic, no HTTP concerns
/// </summary>
public class AuditLogFilter : BaseFilter
{
    /// <summary>
    /// Filter audit logs from this date (yyyy-MM-dd format)
    /// </summary>
    public string? DateFrom { get; set; }

    /// <summary>
    /// Filter audit logs to this date (yyyy-MM-dd format)
    /// </summary>
    public string? DateTo { get; set; }

    /// <summary>
    /// Filter by action type (CREATE, UPDATE, DELETE, etc.)
    /// </summary>
    public string? Action { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize action
        if (!string.IsNullOrWhiteSpace(Action))
        {
            Action = Action.ToUpperInvariant();
        }

        // Validate date format (basic validation)
        if (!string.IsNullOrWhiteSpace(DateFrom) && !DateTime.TryParse(DateFrom, out _))
        {
            DateFrom = null; // Invalid date, ignore
        }

        if (!string.IsNullOrWhiteSpace(DateTo) && !DateTime.TryParse(DateTo, out _))
        {
            DateTo = null; // Invalid date, ignore
        }
    }
}

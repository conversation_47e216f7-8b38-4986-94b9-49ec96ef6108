using System.ComponentModel.DataAnnotations;
namespace Kantoku.Persistence.Filters.Domains;

public class ProjectScheduleFilter : BaseFilter
{
    [FromQuery(Name = "searchKeyword")]
    public string? SearchKeyword { get; set; }

    [FromQuery(Name = "fromDate")]
    [Required]
    public DateOnly FromDate { get; set; }

    [FromQuery(Name = "toDate")]
    [Required]
    public DateOnly ToDate { get; set; }
}


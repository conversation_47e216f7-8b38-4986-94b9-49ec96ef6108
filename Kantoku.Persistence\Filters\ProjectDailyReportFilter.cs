namespace Kantoku.Persistence.Filters.Domains;

public class ProjectDailyReportFilter : BaseFilter
{
    [FromQuery(Name = "keyword")]
    public string? Keyword { get; set; }

    [FromQuery(Name = "projectId")]
    public Guid? ProjectId { get; set; }

    [FromQuery(Name = "constructionId")]
    public Guid? ConstructionId { get; set; }

    [FromQuery(Name = "reportFrom")]
    public string? ReportFrom { get; set; }

    [FromQuery(Name = "reportTo")]
    public string? ReportTo { get; set; }

    [FromQuery(Name = "reportDate")]
    public string? ReportDate { get; set; }
}

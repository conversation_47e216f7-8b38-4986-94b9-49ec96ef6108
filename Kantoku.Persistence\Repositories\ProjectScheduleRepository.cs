using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface IProjectScheduleRepository
{
    Task<ProjectSchedule?> GetById(Guid projectScheduleId, ScheduleQueryableOptions options);
    Task<ProjectSchedule?> GetByProjectIdAndDate(Guid projectId, DateOnly workingDate, ScheduleQueryableOptions options);
    Task<IEnumerable<ProjectSchedule>> GetByProjectIdAndDateRange(
        Guid projectId, DateOnly dateFrom, DateOnly dateTo, ScheduleQueryableOptions options);
    Task<IEnumerable<ProjectSchedule>> GetByFilters(ProjectScheduleFilter filter, ScheduleQueryableOptions options);
    Task<bool> IsExist(Guid projectId, DateOnly workingDate);

    Task<ProjectSchedule?> Create(ProjectSchedule projectSchedule, ScheduleQueryableOptions? options = null);
    Task<ProjectSchedule?> Update(ProjectSchedule projectSchedule, ScheduleQueryableOptions? options = null);
    Task<IEnumerable<ProjectSchedule>> UpdateMultiple(IEnumerable<ProjectSchedule> projectSchedules, ScheduleQueryableOptions? options = null);
    Task<bool> Delete(ProjectSchedule projectSchedule);
    Task<bool> DeleteMultiple(IEnumerable<ProjectSchedule> projectSchedules);
}

public class ProjectScheduleRepository : BaseRepository<ProjectSchedule>, IProjectScheduleRepository
{
    private readonly IScheduleQueryable scheduleQueryable;

    public ProjectScheduleRepository(
        PostgreDbContext context,
        IScheduleQueryable scheduleQueryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.scheduleQueryable = scheduleQueryable;
    }

    public async Task<ProjectSchedule?> GetById(Guid projectScheduleId, ScheduleQueryableOptions options)
    {
        try
        {
            var query = scheduleQueryable.GetSchedulesQueryIncluded(options: options)
                .Where(ps => ps.ProjectScheduleUid == projectScheduleId);

            var ps = await query.FirstOrDefaultAsync();
            return ps;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting project schedule for project {ProjectId}", projectScheduleId);
            return null;
        }
    }

    public async Task<ProjectSchedule?> GetByProjectIdAndDate(Guid projectId, DateOnly workingDate, ScheduleQueryableOptions options)
    {
        try
        {
            var query = scheduleQueryable.GetSchedulesQueryIncluded(options)
                .Where(ps => ps.ProjectUid == projectId)
                .Where(ps => ps.WorkingDate == workingDate);
            var ps = await query.FirstOrDefaultAsync();
            return ps;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting project schedule for project {ProjectId} on date {Date}", projectId, workingDate);
            return null;
        }
    }

    public async Task<IEnumerable<ProjectSchedule>> GetByFilters(ProjectScheduleFilter filter, ScheduleQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = scheduleQueryable.GetSchedulesQueryByFilters(filter, options)
                .Where(ps => ps.Project.StatusCode == StatusConstants.STARTED);
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting project schedules by filters");
            return [];
        }
    }

    public async Task<IEnumerable<ProjectSchedule>> GetByProjectIdAndDateRange(
        Guid projectId, DateOnly dateFrom, DateOnly dateTo, ScheduleQueryableOptions options)
    {
        try
        {
            var query = scheduleQueryable.GetSchedulesQueryIncluded(options)
                .Where(ps => ps.WorkingDate >= dateFrom && ps.WorkingDate <= dateTo);
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting project schedule for project {ProjectId} on date {Date}", projectId, dateFrom);
            return [];
        }
    }

    public async Task<bool> IsExist(Guid projectId, DateOnly workingDate)
    {
        try
        {
            return await context.ProjectSchedules
                .AnyAsync(ps => ps.ProjectUid == projectId && ps.WorkingDate == workingDate);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error checking if project schedule exists for project {ProjectId} on date {Date}", projectId, workingDate);
            return false;
        }
    }

    public async Task<ProjectSchedule?> Create(ProjectSchedule projectSchedule, ScheduleQueryableOptions? options = null)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.ProjectSchedules.AddAsync(projectSchedule);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(projectSchedule.ProjectScheduleUid, options ?? new ScheduleQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating project schedule for project {ProjectId} on date {Date}", projectSchedule.ProjectUid, projectSchedule.WorkingDate);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<ProjectSchedule?> Update(ProjectSchedule projectSchedule, ScheduleQueryableOptions? options = null)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.ProjectSchedules.Update(projectSchedule);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(projectSchedule.ProjectScheduleUid, options ?? new ScheduleQueryableOptions());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating project schedule for project {ProjectId} on date {Date}", projectSchedule.ProjectUid, projectSchedule.WorkingDate);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<IEnumerable<ProjectSchedule>> UpdateMultiple(IEnumerable<ProjectSchedule> projectSchedules, ScheduleQueryableOptions? options = null)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.ProjectSchedules.UpdateRange(projectSchedules);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetProjectSchedulesByIds(
                    projectSchedules.Select(ps => ps.ProjectScheduleUid),
                    options ?? new ScheduleQueryableOptions()
                );
            }
            await transaction.RollbackAsync();
            return [];
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating project schedules");
            await transaction.RollbackAsync();
            return [];
        }
    }

    public async Task<bool> Delete(ProjectSchedule projectSchedule)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            projectSchedule.IsDeleted = true;
            context.Entry(projectSchedule).Property(ps => ps.IsDeleted).IsModified = true;
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error deleting project schedule for project {ProjectUid}", projectSchedule.ProjectUid);
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<bool> DeleteMultiple(IEnumerable<ProjectSchedule> projectSchedules)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            foreach (var ps in projectSchedules)
            {
                ps.IsDeleted = true;
                context.Entry(ps).Property(ps => ps.IsDeleted).IsModified = true;
            }
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error deleting project schedules");
            return false;
        }
    }

    private async Task<IEnumerable<ProjectSchedule>> GetProjectSchedulesByIds(IEnumerable<Guid> projectScheduleUids, ScheduleQueryableOptions options)
    {
        try
        {
            var query = scheduleQueryable.GetSchedulesQueryIncluded(options)
                .Where(ps => projectScheduleUids.Contains(ps.ProjectScheduleUid));
            return await query.ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting project schedules by ids {ProjectScheduleUids}", projectScheduleUids);
            return [];
        }
    }
}
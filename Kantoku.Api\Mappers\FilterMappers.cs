using Kantoku.Persistence.Filters;
using ApiFilters = Kantoku.Api.Filters.Domains;
using DomainFilters = Kantoku.Persistence.Filters;

namespace Kantoku.Api.Mappers;

/// <summary>
/// Maps API employee filter to domain employee filter
/// </summary>
public class EmployeeFilterMapper : FilterMapperBase<ApiFilters.EmployeeFilter, DomainFilters.EmployeeFilter>
{
    protected override void MapSpecificProperties(ApiFilters.EmployeeFilter apiFilter, DomainFilters.EmployeeFilter domainFilter)
    {
        domainFilter.Keyword = apiFilter.Keyword;
        domainFilter.EmployeeType = apiFilter.EmployeeType;
        domainFilter.StructureId = apiFilter.StructureId;
        domainFilter.PositionId = apiFilter.PositionId;
        domainFilter.RankId = apiFilter.RankId;
        domainFilter.WorkingStatus = apiFilter.WorkingStatus;
        domainFilter.HasApprovalAuthority = apiFilter.HasApprovalAuthority;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Maps API audit log filter to domain audit log filter
/// </summary>
public class AuditLogFilterMapper : FilterMapperBase<ApiFilters.AuditLogFilter, DomainFilters.AuditLogFilter>
{
    protected override void MapSpecificProperties(ApiFilters.AuditLogFilter apiFilter, DomainFilters.AuditLogFilter domainFilter)
    {
        domainFilter.DateFrom = apiFilter.DateFrom;
        domainFilter.DateTo = apiFilter.DateTo;
        domainFilter.Action = apiFilter.Action;
        domainFilter.PageNum = apiFilter.PageNum;
        domainFilter.PageSize = apiFilter.PageSize;
    }
}

/// <summary>
/// Extension methods for easy filter mapping
/// </summary>
public static class FilterMappingExtensions
{
    private static readonly EmployeeFilterMapper _employeeMapper = new();
    private static readonly AuditLogFilterMapper _auditLogMapper = new();

    /// <summary>
    /// Maps API employee filter to domain filter
    /// </summary>
    public static DomainFilters.EmployeeFilter ToDomain(this ApiFilters.EmployeeFilter apiFilter)
    {
        return _employeeMapper.MapToDomain(apiFilter);
    }

    /// <summary>
    /// Maps API audit log filter to domain filter
    /// </summary>
    public static DomainFilters.AuditLogFilter ToDomain(this ApiFilters.AuditLogFilter apiFilter)
    {
        return _auditLogMapper.MapToDomain(apiFilter);
    }
}

/// <summary>
/// Service for registering all filter mappers
/// </summary>
public static class FilterMapperRegistration
{
    public static IServiceCollection AddFilterMappers(this IServiceCollection services)
    {
        services.AddScoped<IFilterMapper<ApiFilters.EmployeeFilter, DomainFilters.EmployeeFilter>, EmployeeFilterMapper>();
        services.AddScoped<IFilterMapper<ApiFilters.AuditLogFilter, DomainFilters.AuditLogFilter>, AuditLogFilterMapper>();
        
        // Add more mappers as needed...
        
        return services;
    }
}

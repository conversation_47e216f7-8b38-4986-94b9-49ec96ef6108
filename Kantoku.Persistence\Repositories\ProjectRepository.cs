using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Persistence.Repositories;

public interface IProjectRepository : IBaseRepository<Project>
{
    Task<Project?> GetById(Guid projectId, ProjectQueryableOptions options);
    Task<(IEnumerable<Project>, int)> GetByFilter(ProjectFilter filter, ProjectQueryableOptions options);
    Task<IEnumerable<Project>> GetSimpleProjectsByManagerId(Guid managerId, ProjectQueryableOptions options);
    Task<(IEnumerable<Project>, int)> GetProjectsByManagerId(Guid managerId, int pageNum, int pageSize, ProjectQueryableOptions options);
    Task<Project?> Create(Project project, ProjectQueryableOptions options);
    Task<Project?> Update(Project project, ProjectQueryableOptions options);
    Task<bool> UpdateManagers(IEnumerable<ProjectManager> newProjectManagers, IEnumerable<ProjectManager> oldProjectManagers);
    Task<bool> UpdateWorkShifts(IEnumerable<ProjectWorkShift> newProjectWorkShifts, IEnumerable<ProjectWorkShift> oldProjectWorkShifts);
}

public class ProjectRepository : BaseRepository<Project>, IProjectRepository
{
    private readonly IProjectQueryable projectQueryable;

    public ProjectRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        IProjectQueryable projectQueryable,
        ITenantContext tenantContext
    ) : base(context, logger, tenantContext)
    {
        this.projectQueryable = projectQueryable;
    }

    public async Task<Project?> GetById(Guid projectId, ProjectQueryableOptions options)
    {
        try
        {
            var query = projectQueryable.GetProjectsQueryIncluded(options)
                .Where(p => p.ProjectUid == projectId);

            return await query
                .FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting project by id {ProjectId}", projectId);
            return null;
        }
    }

    public async Task<(IEnumerable<Project>, int)> GetByFilter(ProjectFilter filter, ProjectQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = projectQueryable.GetProjectsQueryFiltered(options, filter);

            var projects = await query
                .OrderBy(p => p.ProjectUid)
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var totalProjects = await query.CountAsync();

            return (projects, totalProjects);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting paginated projects");
            return ([], 0);
        }
    }

    public async Task<IEnumerable<Project>> GetSimpleProjectsByManagerId(Guid managerId, ProjectQueryableOptions options)
    {
        try
        {
            var query = projectQueryable.GetProjectsQueryIncluded(options)
                .Where(p => p.StatusCode.Equals(StatusConstants.STARTED))
                .Where(p => p.Managers.Any(pm => pm.EmployeeUid == managerId));

            var managedProjects = await query
                .Select(p => new Project
                {
                    ProjectUid = p.ProjectUid,
                    ProjectCode = p.ProjectCode,
                    ProjectName = p.ProjectName,
                    Address = p.Address,
                })
                .ToListAsync();
            return managedProjects;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting projects by manager id {ManagerId}", managerId);
            return [];
        }
    }

    public async Task<(IEnumerable<Project>, int)> GetProjectsByManagerId(
        Guid managerId,
        int pageNum,
        int pageSize,
        ProjectQueryableOptions options
    )
    {
        try
        {
            var query = projectQueryable.GetProjectsQueryIncluded(options)
                .Where(p => p.StatusCode.Equals(StatusConstants.STARTED))
                .Where(p => p.Managers.Any(pm => pm.EmployeeUid == managerId));

            var managedProjects = await query
                .OrderBy(p => p.ProjectCode)
                .Skip((pageNum - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var total = await query.CountAsync();

            return (managedProjects, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting projects by manager id {ManagerId}", managerId);
            return ([], 0);
        }
    }

    public async Task<Project?> Create(Project project, ProjectQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.Projects.AddAsync(project);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(project.ProjectUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating project {Project}", project);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<Project?> Update(Project project, ProjectQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Projects.Update(project);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(project.ProjectUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating project {ProjectUid}", project.ProjectUid);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<bool> UpdateManagers(IEnumerable<ProjectManager> newProjectManagers, IEnumerable<ProjectManager> oldProjectManagers)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.ProjectManagers.RemoveRange(oldProjectManagers);
            context.ProjectManagers.AddRange(newProjectManagers);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating managers");
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<bool> UpdateWorkShifts(IEnumerable<ProjectWorkShift> newProjectWorkShifts, IEnumerable<ProjectWorkShift> oldProjectWorkShifts)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.ProjectWorkShifts.RemoveRange(oldProjectWorkShifts);
            context.ProjectWorkShifts.AddRange(newProjectWorkShifts);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating work shifts");
            await transaction.RollbackAsync();
            return false;
        }
    }
}
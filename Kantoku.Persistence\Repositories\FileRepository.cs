using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using File = Kantoku.Persistence.Models.File;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface IFileRepository
{
    Task<File?> GetById(string fileUid);
    Task<File?> GetByFileName(string fileName);
    Task<File?> GetByFileUrl(string fileUrl);
    Task<(IEnumerable<File>, int)> GetByPath(string path, int pageNum, int pageSize);
    Task<File?> Create(File file);
    Task<IEnumerable<File>> Upsert(IEnumerable<File> files);
    Task<File?> Update(File file);
    Task<bool> Delete(string fileUrl);
    Task<bool> Delete(IEnumerable<string> fileUrls);
}

public class FileRepository : BaseRepository<File>, IFileRepository
{
    public FileRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
    }

    public async Task<File?> GetById(string fileUid)
    {
        if (!Guid.TryParse(fileUid, out var fileUidGuid))
        {
            logger.Error("Invalid file uid: {FileUid}", fileUid);
            return null;
        }
        try
        {
            return await context.Files
                .Where(f => f.IsDeleted == false)
                .Where(f => f.OrgUid == GetCurrentOrgUid())
                .FirstOrDefaultAsync(f => f.FileUid == fileUidGuid);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting file by file uid: {FileUid}", fileUid);
            return null;
        }
    }

    public async Task<File?> GetByFileName(string fileName)
    {
        try
        {
            return await context.Files
                .Where(f => f.IsDeleted == false)
                .Where(f => f.OrgUid == GetCurrentOrgUid())
                .FirstOrDefaultAsync(f => f.FileName == fileName);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting file by file name: {FileName}", fileName);
            return null;
        }
    }

    public async Task<File?> GetByFileUrl(string fileUrl)
    {
        try
        {
            return await context.Files
                .Where(f => f.IsDeleted == false)
                .Where(f => f.OrgUid == GetCurrentOrgUid())
                .FirstOrDefaultAsync(f => f.FileUrl == fileUrl);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting file by file url: {FileUrl}", fileUrl);
            return null;
        }
    }

    public async Task<(IEnumerable<File>, int)> GetByPath(string path, int pageNum, int pageSize)
    {
        try
        {
            var files = await context.Files
                .Where(f => f.IsDeleted == false)
                .Where(f => f.OrgUid == GetCurrentOrgUid())
                .Where(f => f.FileUrl.Contains(path))
                .OrderByDescending(f => f.CreatedTime)
                .Skip((pageNum - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (files, files.Count);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting files by path: {Path}", path);
            return (new List<File>(), 0);
        }
    }

    public async Task<File?> Create(File file)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            file.IsDeleted = false;
            file.FileUid = GuidHelper.GenerateUUIDv7();
            file.OrgUid = GetCurrentOrgUid();
            await context.Files.AddAsync(file);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(file.FileUid.ToString());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating file: {FileName}", file.FileName);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<File?> Update(File file)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.Files.Update(file);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(file.FileUid.ToString());
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating file: {FileName}", file.FileName);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<IEnumerable<File>> Upsert(IEnumerable<File> files)
    {
        try
        {
            var fileUrls = files.Select(f => f.FileUrl).ToList();

            var existingFiles = await context.Files
                    .Where(f => fileUrls.Contains(f.FileUrl))
                    .ToDictionaryAsync(f => f.FileUrl);

            var filesToUpdate = new List<File>();
            var filesToCreate = new List<File>();

            foreach (var file in files)
            {
                if (existingFiles.TryGetValue(file.FileUrl, out var existingFile))
                {
                    existingFile.FileName = file.FileName;
                    existingFile.FileSize = file.FileSize;
                    existingFile.FileType = file.FileType;
                    existingFile.FileMetadata = file.FileMetadata;

                    filesToUpdate.Add(existingFile);
                }
                else
                {
                    file.FileUid = GuidHelper.GenerateUUIDv7();
                    file.OrgUid = GetCurrentOrgUid();
                    file.IsDeleted = false;

                    filesToCreate.Add(file);
                }
            }

            if (filesToUpdate.Any())
            {
                context.Files.UpdateRange(filesToUpdate);
            }

            if (filesToCreate.Any())
            {
                await context.Files.AddRangeAsync(filesToCreate);
            }

            await context.SaveChangesAsync();

            return filesToUpdate.Concat(filesToCreate);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error during file upsert operation");
            throw;
        }
    }


    public async Task<bool> Delete(string fileUrl)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            var file = await GetByFileUrl(fileUrl);
            if (file is null)
            {
                logger.Error("File not found for {FileUrl}", fileUrl);
                return false;
            }
            file.IsDeleted = true;
            context.Entry(file).Property(f => f.IsDeleted).IsModified = true;
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error deleting file: {FileUrl}", fileUrl);
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<bool> Delete(IEnumerable<string> fileUrls)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            var files = await context.Files
                .Where(f => fileUrls.Contains(f.FileUrl))
                .ToListAsync();
            foreach (var file in files)
            {
                file.IsDeleted = true;
                context.Entry(file).Property(f => f.IsDeleted).IsModified = true;
            }
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error deleting files: {FileUrls}", fileUrls);
            await transaction.RollbackAsync();
            return false;
        }
    }
}

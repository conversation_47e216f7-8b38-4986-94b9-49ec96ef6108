using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Queryables;
using Kantoku.Persistence.Services;

namespace Kantoku.Persistence.Repositories;

public interface IProjectDailyReportRepository
{
    Task<(IEnumerable<ProjectDailyReport>, int)> GetByFilter(ProjectDailyReportFilter filter, ProjectDailyReportQueryableOptions options);
    Task<ProjectDailyReport?> GetByProjectIdAndDate(Guid projectId, DateOnly reportDate, ProjectDailyReportQueryableOptions options);
    Task<(IEnumerable<ProjectDailyReport>, int)> GetByProjectInDateRange(Guid projectId, ProjectDailyReportFilter filter, ProjectDailyReportQueryableOptions options);
    Task<bool> IsExist(Guid projectId, DateOnly reportDate);
    Task<ProjectDailyReport?> GetById(Guid reportId, ProjectDailyReportQueryableOptions options);
    Task<ProjectDailyReport?> Create(ProjectDailyReport projectDailyReport, ProjectDailyReportQueryableOptions options);
    Task<ProjectDailyReport?> Update(ProjectDailyReport projectDailyReport, ProjectDailyReportQueryableOptions options);
}

public class ProjectDailyReportRepository : BaseRepository<ProjectDailyReport>, IProjectDailyReportRepository
{
    private readonly IProjectDailyReportQueryable projectDailyReportQueryable;
    public ProjectDailyReportRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext,
        IProjectDailyReportQueryable projectDailyReportQueryable
    )
        : base(context, logger, tenantContext)
    {
        this.projectDailyReportQueryable = projectDailyReportQueryable;
    }


    public async Task<(IEnumerable<ProjectDailyReport>, int)> GetByFilter(ProjectDailyReportFilter filter, ProjectDailyReportQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = projectDailyReportQueryable.GetProjectDailyReportQueryFiltered(filter, options);

            var projectDailyReports = await query
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var total = await query.CountAsync();

            return (projectDailyReports, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting all projectDailyReports");
            return (new List<ProjectDailyReport>(), 0);
        }
    }

    public async Task<(IEnumerable<ProjectDailyReport>, int)> GetByProjectInDateRange(Guid projectId, ProjectDailyReportFilter filter, ProjectDailyReportQueryableOptions options)
    {
        try
        {
            var query = projectDailyReportQueryable.GetProjectDailyReportQueryFiltered(filter, options)
                .Where(pdr => pdr.ProjectUid == projectId);

            var projectDailyReports = await query.ToListAsync();
            var total = await query.CountAsync();

            return (projectDailyReports, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting projectDailyReports by projectId {ProjectId} and date range {ReportFrom} to {ReportTo}",
                projectId, filter.ReportFrom, filter.ReportTo);
            return (new List<ProjectDailyReport>(), 0);
        }
    }

    public async Task<ProjectDailyReport?> GetByProjectIdAndDate(Guid projectId, DateOnly reportDate, ProjectDailyReportQueryableOptions options)
    {
        try
        {
            var query = projectDailyReportQueryable.GetProjectDailyReportQueryableIncluded(options)
                .Where(pdr => pdr.ProjectUid == projectId && pdr.ReportDate == reportDate);

            var projectDailyReport = await query.FirstOrDefaultAsync();

            return projectDailyReport;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting projectDailyReport by projectId {ProjectId} and reportDate {ReportDate}", projectId, reportDate);
            return null;
        }
    }

    public async Task<ProjectDailyReport?> GetById(Guid projectDailyReportId, ProjectDailyReportQueryableOptions options)
    {
        try
        {
            var query = projectDailyReportQueryable.GetProjectDailyReportQueryableIncluded(options)
                .Where(p => p.ProjectDailyReportUid == projectDailyReportId);

            var result = await query.FirstOrDefaultAsync();
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting projectDailyReport by id {ProjectDailyReportId}", projectDailyReportId);
            return null;
        }
    }

    public async Task<bool> IsExist(Guid projectId, DateOnly reportDate)
    {
        try
        {
            var query = projectDailyReportQueryable.GetProjectDailyReportQueryable(new ProjectDailyReportQueryableOptions())
                .Where(pdr => pdr.ProjectUid == projectId && pdr.ReportDate == reportDate);

            var result = await query.AnyAsync();
            return result;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error checking if projectDailyReport exists");
            return false;
        }
    }

    public async Task<ProjectDailyReport?> Create(ProjectDailyReport projectDailyReport, ProjectDailyReportQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.ProjectDailyReports.AddAsync(projectDailyReport);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(projectDailyReport.ProjectDailyReportUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating projectDailyReport {ProjectDailyReport}", projectDailyReport);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<ProjectDailyReport?> Update(ProjectDailyReport projectDailyReport, ProjectDailyReportQueryableOptions options)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.ProjectDailyReports.Update(projectDailyReport);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return await GetById(projectDailyReport.ProjectDailyReportUid, options);
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating projectDailyReport {ProjectDailyReport}", projectDailyReport);
            await transaction.RollbackAsync();
            return null;
        }
    }

    // public async Task UpdateCategorizedCost(ProjectDailyReport dailyReport)
    // {
    //     var project = await context.Projects
    //         .Include(p => p.Constructions)
    //         .Where(p => p.ProjectUid == dailyReport.ProjectUid)
    //         .Select(p => new
    //         {
    //             p.ProjectUid,
    //             p.ProjectName,
    //             p.OrgUid,
    //             p.MonthlyReportDate,
    //             p.Constructions
    //         })
    //         .FirstOrDefaultAsync();
    //     if (project is null)
    //     {
    //         logger.Error("Project not found");
    //         return;
    //     }

    //     var org = await context.Orgs.FindAsync(project.OrgUid);
    //     if (org is null)
    //     {
    //         logger.Error("Org not found");
    //         return;
    //     }
    //     var employeeRankingDefinitionType = org.EmployeeRankingDefinitionType;

    //     var primaryConstruction = project.Constructions.FirstOrDefault(c => c.IsPrimary);
    //     if (primaryConstruction is null)
    //     {
    //         primaryConstruction = new Construction
    //         {
    //             ConstructionUid = GuidHelper.GenerateUUIDv7(),
    //             ProjectUid = project.ProjectUid,
    //             ConstructionName = project.ProjectName,
    //             IsPrimary = true,
    //             OrgUid = project.OrgUid,
    //             IsDeleted = false,
    //         };
    //         await context.Constructions.AddAsync(primaryConstruction);
    //         await context.SaveChangesAsync();
    //     }

    //     var subConstruction = project.Constructions.FirstOrDefault(c => c.IsPrimary == false);
    //     if (subConstruction is null)
    //     {
    //         subConstruction = new Construction
    //         {
    //             ConstructionUid = GuidHelper.GenerateUUIDv7(),
    //             ProjectUid = project.ProjectUid,
    //             ConstructionName = $"{project.ProjectName} - Sub",
    //             IsPrimary = false,
    //             OrgUid = project.OrgUid,
    //             IsDeleted = false,
    //         };
    //         await context.Constructions.AddAsync(subConstruction);
    //         await context.SaveChangesAsync();
    //     }

    //     // range of project report date
    //     var reportFrom = new DateOnly(dailyReport.ReportDate.Year, dailyReport.ReportDate.Month, project.MonthlyReportDate);
    //     var reportTo = reportFrom.AddMonths(1).AddDays(-1);

    //     //recalculate all daily reports in the range
    //     var dailyReports = await context.ProjectDailyReports
    //         .Where(d => d.ProjectUid == dailyReport.ProjectUid && d.ReportDate >= reportFrom && d.ReportDate <= reportTo)
    //         .ToListAsync();

    //     var employeeWorkloads = dailyReports.SelectMany(d => d.EmployeeWorkload ?? []).ToList();
    //     var outSourceWorkloads = dailyReports.SelectMany(d => d.OutSourceWorkload ?? []).ToList();

    //     var mainConsEmployeeWorkloads = employeeWorkloads.Select(e => new
    //     {
    //         e.EmployeeUid,
    //         e.MainConsWorkload,
    //     }).ToList();

    //     var subConsEmployeeWorkloads = employeeWorkloads.Select(e => new
    //     {
    //         e.EmployeeUid,
    //         e.SubConsWorkload,
    //     }).ToList();

    //     var mainConsOutSourceWorkloads = outSourceWorkloads.Select(o => new
    //     {
    //         o.OutSourceUid,
    //         o.MainConsWorkload,
    //     }).ToList();

    //     var subConsOutSourceWorkloads = outSourceWorkloads.Select(o => new
    //     {
    //         o.OutSourceUid,
    //         o.SubConsWorkload,
    //     }).ToList();

    //     var categoryEmployee = await context.Categories.FindAsync(CategoryConstant.EMPLOYEE);
    //     var categoryOutSource = await context.Categories.FindAsync(CategoryConstant.OUTSOURCE);
    //     var rankValidFrom = new DateOnly(dailyReport.ReportDate.Year, dailyReport.ReportDate.Month, 1);
    //     var rankValidTo = rankValidFrom.AddMonths(1).AddDays(-1);

    //     foreach (var employeeWorkload in employeeWorkloads)
    //     {
    //         var employee = await context.Employees.FindAsync(employeeWorkload.EmployeeUid);
    //         if (employee is null)
    //         {
    //             logger.Error("Employee not exist, skip update categorized cost");
    //             continue;
    //         }
    //         var employeeRank = await context.EmployeeRanks
    //             .Where(er => er.EmployeeUid == employee.EmployeeUid
    //             && er.RankValidDateFrom == rankValidFrom
    //             && er.RankValidDateTo == rankValidTo)
    //             .FirstOrDefaultAsync();

    //         if (employeeRank is null)
    //         {
    //             logger.Error("Employee rank not set, skip update categorized cost");
    //             continue;
    //         }






    //     }

    //     var constructionCost = mainConstruction.ConstructionCosts.FirstOrDefault(c => c.StartDate == reportFrom && c.EndDate == reportTo);
    //     if (constructionCost is null)
    //     {
    //         constructionCost = new ConstructionCost
    //         {
    //             ConstructionCostUid = GuidHelper.GenerateUUIDv7(),
    //             ConstructionUid = constructionId,
    //             OrgUid = project.OrgUid,
    //             StartDate = reportFrom,
    //             EndDate = reportTo,
    //             RiskModifiedAmount = 0,
    //             IsDeleted = false,
    //         };

    //         var currentCategory = inputCostItem.Item.CategoryUid;

    //         var inputCostItems = await context.InputCostItems
    //             .Include(i => i.Item)
    //             .ThenInclude(i => i.Category)
    //             .Where(i => i.ConstructionUid == constructionId
    //             && i.IsDeleted == false
    //             && i.TransactionDate >= reportFrom && i.TransactionDate <= reportTo
    //             && i.Item.CategoryUid == currentCategory)
    //             .ToListAsync();

    //         var categorizedCost = inputCostItems.Select(i => new CategorizedCost
    //         {
    //             CategorizedCostUid = GuidHelper.GenerateUUIDv7(),
    //             ConstructionCostUid = constructionCost.ConstructionCostUid,
    //             CategoryUid = i.Item.CategoryUid,
    //             TotalAmount = i.TotalTaxed ?? i.TotalNonTaxed ?? (long)(i.Quantity * i.Price * (1 + (i.TaxRate ?? 0))),
    //         });

    //         constructionCost.TotalCostAmount = categorizedCost.Sum(c => c.TotalAmount) + constructionCost.RiskModifiedAmount;
    //         constructionCost.CategorizedCosts = categorizedCost.ToList();

    //         using var transaction = await context.Database.BeginTransactionAsync();
    //         try
    //         {
    //             await context.ConstructionCosts.AddAsync(constructionCost);
    //             await context.SaveChangesAsync();
    //             await transaction.CommitAsync();
    //         }
    //         catch (Exception ex)
    //         {
    //             logger.Error(ex, "Error update categorized cost");
    //             await transaction.RollbackAsync();
    //         }
    //     }
    // }
}


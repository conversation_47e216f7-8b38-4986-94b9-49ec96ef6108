namespace Kantoku.Persistence.Filters;

/// <summary>
/// Domain filter for employee queries - contains only business logic, no HTTP concerns
/// </summary>
public class EmployeeFilter : BaseFilter
{
    /// <summary>
    /// Keyword to search in employee name, code, or email
    /// </summary>
    public string? Keyword { get; set; }

    /// <summary>
    /// Filter by employee type (true for full-time, false for part-time, null for all)
    /// </summary>
    public bool? EmployeeType { get; set; }

    /// <summary>
    /// Filter by organizational structure ID
    /// </summary>
    public Guid? StructureId { get; set; }

    /// <summary>
    /// Filter by position ID
    /// </summary>
    public Guid? PositionId { get; set; }

    /// <summary>
    /// Filter by rank ID
    /// </summary>
    public Guid? RankId { get; set; }

    /// <summary>
    /// Filter by working status (ACTIVE, INACTIVE, etc.)
    /// </summary>
    public string? WorkingStatus { get; set; }

    /// <summary>
    /// Filter by approval authority
    /// </summary>
    public bool? HasApprovalAuthority { get; set; }

    public override void Validate()
    {
        base.Validate();

        // Normalize keyword
        if (!string.IsNullOrWhiteSpace(Keyword))
        {
            Keyword = Keyword.Trim();
        }

        // Normalize working status
        if (!string.IsNullOrWhiteSpace(WorkingStatus))
        {
            WorkingStatus = WorkingStatus.ToUpperInvariant();
        }
    }
}

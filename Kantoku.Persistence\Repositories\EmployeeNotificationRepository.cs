using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Kantoku.Persistence.Services;
using Kantoku.Persistence.Queryables;

namespace Kantoku.Persistence.Repositories;

public interface IEmployeeNotificationRepository
{
    Task<(IEnumerable<EmployeeNotification>, int)> GetByFilter(Guid employeeId, EmployeeNotificationFilter filter, EmployeeNotificationQueryableOptions options);
    Task<IEnumerable<EmployeeNotification>> GetByEmployeeId(Guid employeeId, EmployeeNotificationQueryableOptions options);
    Task<EmployeeNotification?> GetById(Guid employeeNotificationId, EmployeeNotificationQueryableOptions options);
    Task<Guid?> Create(EmployeeNotification employeeNotification);
    Task<IEnumerable<Guid>> Create(IEnumerable<EmployeeNotification> employeeNotifications);
    Task<bool> Update(EmployeeNotification employeeNotification);
    Task<bool> UpdateRange(IEnumerable<EmployeeNotification> employeeNotifications);
}

public class EmployeeNotificationRepository : BaseRepository<EmployeeNotification>, IEmployeeNotificationRepository
{
    private readonly IEmployeeNotificationQueryable employeeNotificationQueryable;
    public EmployeeNotificationRepository(
        PostgreDbContext context,
        IEmployeeNotificationQueryable employeeNotificationQueryable,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    )
        : base(context, logger, tenantContext)
    {
        this.employeeNotificationQueryable = employeeNotificationQueryable;
    }

    public async Task<(IEnumerable<EmployeeNotification>, int)> GetByFilter(Guid employeeId, EmployeeNotificationFilter filter, EmployeeNotificationQueryableOptions options)
    {
        try
        {
            filter = ValidateFilter(filter);
            var query = employeeNotificationQueryable.GetEmployeeNotificationQueryFiltered(filter, options)
                .Where(n => n.EmployeeUid == employeeId);

            var total = await query.CountAsync();

            var result = await query
                .Skip((filter.PageNum - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();
            return (result, total);
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employeeNotifications");
            return ([], 0);
        }
    }

    public async Task<IEnumerable<EmployeeNotification>> GetByEmployeeId(Guid employeeId, EmployeeNotificationQueryableOptions options)
    {
        try
        {
            return await employeeNotificationQueryable.GetEmployeeNotificationQueryIncluded(options)
                .Where(n => n.EmployeeUid == employeeId)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employeeNotifications");
            return [];
        }
    }

    public async Task<EmployeeNotification?> GetById(Guid employeeNotificationId, EmployeeNotificationQueryableOptions options)
    {
        try
        {
            var query = employeeNotificationQueryable.GetEmployeeNotificationQueryIncluded(options)
            .Where(n => n.EmployeeNotificationUid == employeeNotificationId);

            return await query.FirstOrDefaultAsync();
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error getting employeeNotification by id {EmployeeNotificationId}", employeeNotificationId);
            return null;
        }
    }

    public async Task<Guid?> Create(EmployeeNotification employeeNotification)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.EmployeeNotifications.AddAsync(employeeNotification);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return employeeNotification.EmployeeNotificationUid;
            }
            await transaction.RollbackAsync();
            return null;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating employeeNotification: {EmployeeNotification}", employeeNotification);
            await transaction.RollbackAsync();
            return null;
        }
    }

    public async Task<IEnumerable<Guid>> Create(IEnumerable<EmployeeNotification> employeeNotifications)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            await context.EmployeeNotifications.AddRangeAsync(employeeNotifications);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return employeeNotifications.Select(n => n.EmployeeNotificationUid);
            }
            await transaction.RollbackAsync();
            return [];
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error creating employeeNotifications: {EmployeeNotifications}", employeeNotifications);
            await transaction.RollbackAsync();
            return [];
        }
    }

    public async Task<bool> Update(EmployeeNotification employeeNotification)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EmployeeNotifications.Update(employeeNotification);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating employeeNotification: {EmployeeNotification}", employeeNotification);
            await transaction.RollbackAsync();
            return false;
        }
    }

    public async Task<bool> UpdateRange(IEnumerable<EmployeeNotification> employeeNotifications)
    {
        using var transaction = await context.Database.BeginTransactionAsync();
        try
        {
            context.EmployeeNotifications.UpdateRange(employeeNotifications);
            var affectedRow = await context.SaveChangesAsync();
            if (affectedRow > 0)
            {
                await transaction.CommitAsync();
                return true;
            }
            await transaction.RollbackAsync();
            return false;
        }
        catch (Exception ex)
        {
            logger.Error(ex, "Error updating employeeNotifications: {EmployeeNotifications}", employeeNotifications);
            await transaction.RollbackAsync();
            return false;
        }
    }
}


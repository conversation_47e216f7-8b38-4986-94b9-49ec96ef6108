using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Models;
using Microsoft.EntityFrameworkCore;
using Kantoku.Persistence.Services;


namespace Kantoku.Persistence.Repositories;

public interface IStatusRepository
{
    Task<IEnumerable<Status>> GetAll();
    Task<Status?> GetByCode(string code);
    Task<IEnumerable<Status>> GetByGroup(string group);
}

public class StatusRepository : BaseRepository<Status>, IStatusRepository
{
    public StatusRepository(
        PostgreDbContext context,
        Serilog.ILogger logger,
        ITenantContext tenantContext
    ) : base(context, logger, tenantContext)
    {
    }

    public async Task<IEnumerable<Status>> GetAll()
    {
        try
        {
            return await context.Statuses
                .ToListAsync();
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Failed to get all statuses");
            return [];
        }
    }

    public async Task<IEnumerable<Status>> GetByGroup(string group)
    {
        try
        {
            return await context.Statuses.Where(s => s.Group.Equals(group)).ToListAsync();
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Failed to get status by group {Group}", group);
            return [];
        }
    }

    public async Task<Status?> GetByCode(string code)
    {
        try
        {
            return await context.Statuses
                .FirstOrDefaultAsync(s => s.StatusCode.Equals(code));
        }
        catch (System.Exception ex)
        {
            logger.Error(ex, "Failed to get status by code {Code}", code);
            return null;
        }
    }
}

using Kantoku.Api.Utils.Constants;
using Kantoku.Persistence.Contexts;
using Kantoku.Persistence.Services;
using Microsoft.EntityFrameworkCore;

namespace Kantoku.Api.Services;

/// <summary>
/// HTTP-based implementation of tenant context that reads from HttpContext
/// </summary>
public class HttpTenantContext : ITenantContext
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly PostgreDbContext _context;
    private readonly Serilog.ILogger _logger;

    public HttpTenantContext(
        IHttpContextAccessor httpContextAccessor,
        PostgreDbContext context,
        Serilog.ILogger logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _context = context;
        _logger = logger.ForContext<HttpTenantContext>();
    }

    public Guid GetCurrentAccountUid()
    {
        try
        {
            var accountUidClaim = _httpContextAccessor.HttpContext?.Items[ClaimConstant.ACCOUNT_UID];
            return accountUidClaim is not null && Guid.TryParse(accountUidClaim.ToString(), out Guid accountUid)
                ? accountUid
                : Guid.Empty;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error occurred while getting current account uid");
            return Guid.Empty;
        }
    }

    public Guid GetCurrentOrgUid()
    {
        try
        {
            var orgUidClaim = _httpContextAccessor.HttpContext?.Items[ClaimConstant.ORG_UID];
            return orgUidClaim is not null && Guid.TryParse(orgUidClaim.ToString(), out Guid orgUid)
                ? orgUid
                : Guid.Empty;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error occurred while getting current org uid");
            return Guid.Empty;
        }
    }

    public Guid GetCurrentEmployeeUid()
    {
        try
        {
            var employeeUidClaim = _httpContextAccessor.HttpContext?.Items[ClaimConstant.EMPLOYEE_UID];
            return employeeUidClaim is not null && Guid.TryParse(employeeUidClaim.ToString(), out Guid employeeUid)
                ? employeeUid
                : Guid.Empty;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Error occurred while getting current employee uid");
            return Guid.Empty;
        }
    }

    public bool TryGetCurrentAccountUid(out Guid accountUid)
    {
        var accountUidClaim = _httpContextAccessor.HttpContext?.Items[ClaimConstant.ACCOUNT_UID];
        if (accountUidClaim is null)
        {
            _logger.Warning("Current account uid claim is null");
            accountUid = Guid.Empty;
            return false;
        }

        if (Guid.TryParse(accountUidClaim.ToString(), out accountUid))
        {
            return true;
        }

        _logger.Warning("Current account uid claim is not a valid guid");
        return false;
    }

    public bool TryGetCurrentOrgUid(out Guid orgUid)
    {
        var orgUidClaim = _httpContextAccessor.HttpContext?.Items[ClaimConstant.ORG_UID];
        if (orgUidClaim is null)
        {
            _logger.Warning("Current org uid claim is null");
            orgUid = Guid.Empty;
            return false;
        }

        if (Guid.TryParse(orgUidClaim.ToString(), out orgUid))
        {
            return true;
        }

        _logger.Warning("Current org uid claim is not a valid guid");
        return false;
    }

    public bool TryGetCurrentEmployeeUid(out Guid employeeUid)
    {
        var employeeUidClaim = _httpContextAccessor.HttpContext?.Items[ClaimConstant.EMPLOYEE_UID];
        if (employeeUidClaim is null)
        {
            _logger.Warning("Current employee uid claim is null");
            employeeUid = Guid.Empty;
            return false;
        }

        if (Guid.TryParse(employeeUidClaim.ToString(), out employeeUid))
        {
            return true;
        }

        _logger.Warning("Current employee uid claim is not a valid guid");
        return false;
    }

    public async Task<bool> IsSuperUserAsync()
    {
        var accountUid = GetCurrentAccountUid();
        if (accountUid == Guid.Empty)
        {
            return false;
        }

        var account = await _context.Accounts
            .Where(a => a.AccountUid == accountUid)
            .FirstOrDefaultAsync();

        if (account is null)
        {
            return false;
        }

        return account.AccountType.Equals(AccountTypeConstant.SUPER_USER);
    }

    public async Task<bool> IsOrgSuperUserAsync()
    {
        var accountUid = GetCurrentAccountUid();
        var orgUid = GetCurrentOrgUid();

        if (accountUid == Guid.Empty || orgUid == Guid.Empty)
        {
            return false;
        }

        var employee = await _context.Employees
            .Include(e => e.EmployeeRoles)
            .ThenInclude(er => er.Role)
            .Where(e => e.OrgUid == orgUid && e.AccountUid == accountUid)
            .FirstOrDefaultAsync();

        if (employee is null)
        {
            return false;
        }

        return employee.IsOrgAdmin || employee.IsOrgOwner || employee.IsHidden ||
               employee.EmployeeRoles.Any(er => er.Role.IsHidden);
    }
}
